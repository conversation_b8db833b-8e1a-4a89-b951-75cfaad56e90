<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电站监测 - 光伏发电实时数据</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .header {
            background: #fff;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            font-size: 18px;
            margin-right: 15px;
            color: #666;
        }
        
        .title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-size: 12px;
        }
        
        .real-time-indicator {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #52c41a;
            margin-bottom: 3px;
        }
        
        .current-date {
            color: #666;
            font-size: 11px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #52c41a;
            border-radius: 50%;
            margin-right: 5px;
            animation: blink 2s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .station-info {
            padding: 20px;
            background: white;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #666;
            font-size: 14px;
        }
        
        .info-value {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-normal {
            color: #52c41a;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #eee;
            overflow-x: auto;
        }
        
        .tab {
            flex: 1;
            padding: 15px 8px;
            text-align: center;
            font-size: 13px;
            color: #666;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
            min-width: 80px;
        }
        
        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        
        .chart-container {
            padding: 20px;
            background: white;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .chart-title {
            font-size: 16px;
            color: #333;
        }
        
        .chart-unit {
            font-size: 12px;
            color: #999;
        }
        

        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            margin-right: 5px;
            border-radius: 2px;
        }
        
        .legend-green {
            background: #52c41a;
        }
        
        .legend-blue {
            background: #1890ff;
        }
        

        
        #chart {
            width: 100%;
            height: 300px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1px;
            background: #f0f0f0;
            margin: 20px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .stat-card {
            background: white;
            padding: 15px 10px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card.updated {
            background: #f6ffed;
            border-left: 3px solid #52c41a;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            transition: color 0.3s ease;
        }
        
        .stat-card.updated .stat-value {
            color: #52c41a;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .last-update {
            text-align: center;
            padding: 10px;
            font-size: 12px;
            color: #999;
            background: #fafafa;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 0;
            }
            
            .header {
                padding: 12px 15px;
                flex-direction: row;
                justify-content: space-between;
            }
            
            .header-left {
                flex: 1;
                justify-content: flex-start;
            }
            
            .header-right {
                align-items: flex-end;
            }
            
            .title {
                font-size: 16px;
            }
            
            .real-time-indicator {
                font-size: 11px;
            }
            
            .current-date {
                font-size: 10px;
            }
            
            .station-info {
                padding: 15px;
            }
            
            .chart-container {
                padding: 15px;
            }
            
            .stats-grid {
                margin: 15px;
            }
            
            .stat-card {
                padding: 12px 8px;
            }
            
            .stat-value {
                font-size: 16px;
            }
            
            .tab {
                padding: 12px 6px;
                font-size: 12px;
                min-width: 70px;
            }
            
            .legend {
                gap: 15px;
            }
            
            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <span class="back-btn">‹</span>
                <span class="title">电站监测</span>
            </div>
            <div class="header-right">
                <div class="real-time-indicator">
                    <div class="status-dot"></div>
                    <span>实时监测</span>
                </div>
                <div class="current-date" id="currentDate"></div>
            </div>
        </div>
        
        <div class="station-info">
            <div class="info-row">
                <span class="info-label">发电户名</span>
                <span class="info-value">*宗瑞</span>
            </div>
            <div class="info-row">
                <span class="info-label">装机地址</span>
                <span class="info-value">山西省运城市闻喜县****乡464号</span>
            </div>
            <div class="info-row">
                <span class="info-label">装机容量</span>
                <span class="info-value">6 kW</span>
            </div>
            <div class="info-row">
                <span class="info-label">消纳方式</span>
                <span class="info-value">全额上网</span>
            </div>
            <div class="info-row">
                <span class="info-label">运行状态</span>
                <span class="info-value status-normal">正常</span>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab" data-tab="generation">发电量分析</div>
            <div class="tab" data-tab="power">发电功率分析</div>
            <div class="tab active" data-tab="efficiency">发电效率分析</div>
        </div>
        
        <div class="chart-container">
            <div class="chart-header">
                <div class="chart-title" id="chartTitle">发电效率</div>
                <div class="chart-unit" id="chartUnit">单位：%</div>
            </div>
            

            
            <div class="legend" id="chartLegend">
                <div class="legend-item">
                    <div class="legend-color legend-green"></div>
                    <span>本发电户</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-blue"></div>
                    <span>同地区(县)平均</span>
                </div>
            </div>
            
            <div id="chart"></div>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>
        

    </div>
    <script>
        // 全局变量
        let chart;
        let currentTab = 'efficiency';
        let refreshInterval;
        let lastUpdateTime = new Date();
        let currentDataIndex = 0; // 当前显示的数据日期索引

        // 根据用户提供的真实数据构建数据集
        const baseDataSets = {
            // 2025-08-01
            day1: [
                {hour: 0, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 1, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 2, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 3, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 4, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 5, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 6, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 7, power: 0.6, avgPower: 0.5, generation: 0.6, avgGeneration: 0.5, efficiency: 17.6, avgEfficiency: 17.1},
                {hour: 8, power: 2.0, avgPower: 1.8, generation: 2.0, avgGeneration: 1.8, efficiency: 17.9, avgEfficiency: 17.4},
                {hour: 9, power: 3.2, avgPower: 2.9, generation: 3.2, avgGeneration: 2.9, efficiency: 18.1, avgEfficiency: 17.6},
                {hour: 10, power: 4.3, avgPower: 4.0, generation: 4.3, avgGeneration: 4.0, efficiency: 18.0, avgEfficiency: 17.5},
                {hour: 11, power: 5.0, avgPower: 4.7, generation: 5.0, avgGeneration: 4.7, efficiency: 17.8, avgEfficiency: 17.3},
                {hour: 12, power: 5.3, avgPower: 5.0, generation: 5.3, avgGeneration: 5.0, efficiency: 17.6, avgEfficiency: 17.1},
                {hour: 13, power: 5.3, avgPower: 5.0, generation: 5.3, avgGeneration: 5.0, efficiency: 17.5, avgEfficiency: 17.0},
                {hour: 14, power: 4.7, avgPower: 4.4, generation: 4.7, avgGeneration: 4.4, efficiency: 17.7, avgEfficiency: 17.2},
                {hour: 15, power: 3.5, avgPower: 3.2, generation: 3.5, avgGeneration: 3.2, efficiency: 17.9, avgEfficiency: 17.4},
                {hour: 16, power: 2.2, avgPower: 2.0, generation: 2.2, avgGeneration: 2.0, efficiency: 18.1, avgEfficiency: 17.6},
                {hour: 17, power: 1.0, avgPower: 0.9, generation: 1.0, avgGeneration: 0.9, efficiency: 18.3, avgEfficiency: 17.8},
                {hour: 18, power: 0.2, avgPower: 0.2, generation: 0.2, avgGeneration: 0.2, efficiency: 18.2, avgEfficiency: 17.7},
                {hour: 19, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 20, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 21, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 22, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 23, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0}
            ],
            // 2025-08-02
            day2: [
                {hour: 0, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 1, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 2, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 3, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 4, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 5, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 6, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 7, power: 0.4, avgPower: 0.4, generation: 0.4, avgGeneration: 0.4, efficiency: 17.5, avgEfficiency: 17.0},
                {hour: 8, power: 1.7, avgPower: 1.6, generation: 1.7, avgGeneration: 1.6, efficiency: 17.7, avgEfficiency: 17.2},
                {hour: 9, power: 2.8, avgPower: 2.6, generation: 2.8, avgGeneration: 2.6, efficiency: 17.9, avgEfficiency: 17.4},
                {hour: 10, power: 3.7, avgPower: 3.4, generation: 3.7, avgGeneration: 3.4, efficiency: 17.8, avgEfficiency: 17.3},
                {hour: 11, power: 4.3, avgPower: 4.0, generation: 4.3, avgGeneration: 4.0, efficiency: 17.6, avgEfficiency: 17.1},
                {hour: 12, power: 4.6, avgPower: 4.3, generation: 4.6, avgGeneration: 4.3, efficiency: 17.4, avgEfficiency: 16.9},
                {hour: 13, power: 4.6, avgPower: 4.3, generation: 4.6, avgGeneration: 4.3, efficiency: 17.3, avgEfficiency: 16.8},
                {hour: 14, power: 4.0, avgPower: 3.7, generation: 4.0, avgGeneration: 3.7, efficiency: 17.5, avgEfficiency: 17.0},
                {hour: 15, power: 3.0, avgPower: 2.8, generation: 3.0, avgGeneration: 2.8, efficiency: 17.7, avgEfficiency: 17.2},
                {hour: 16, power: 1.9, avgPower: 1.8, generation: 1.9, avgGeneration: 1.8, efficiency: 17.9, avgEfficiency: 17.4},
                {hour: 17, power: 0.9, avgPower: 0.8, generation: 0.9, avgGeneration: 0.8, efficiency: 18.1, avgEfficiency: 17.6},
                {hour: 18, power: 0.1, avgPower: 0.1, generation: 0.1, avgGeneration: 0.1, efficiency: 18.0, avgEfficiency: 17.5},
                {hour: 19, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 20, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 21, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 22, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
                {hour: 23, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0}
            ]
        };

        // 添加更多天的数据
        baseDataSets.day3 = [
            // 2025-08-03 数据
            {hour: 0, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 1, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 2, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 3, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 4, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 5, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 6, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 7, power: 0.6, avgPower: 0.5, generation: 0.6, avgGeneration: 0.5, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 8, power: 2.0, avgPower: 1.8, generation: 2.0, avgGeneration: 1.8, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 9, power: 3.2, avgPower: 2.9, generation: 3.2, avgGeneration: 2.9, efficiency: 18.1, avgEfficiency: 17.6},
            {hour: 10, power: 4.3, avgPower: 4.0, generation: 4.3, avgGeneration: 4.0, efficiency: 18.0, avgEfficiency: 17.5},
            {hour: 11, power: 5.0, avgPower: 4.7, generation: 5.0, avgGeneration: 4.7, efficiency: 17.8, avgEfficiency: 17.3},
            {hour: 12, power: 5.3, avgPower: 5.0, generation: 5.3, avgGeneration: 5.0, efficiency: 17.6, avgEfficiency: 17.1},
            {hour: 13, power: 5.3, avgPower: 5.0, generation: 5.3, avgGeneration: 5.0, efficiency: 17.5, avgEfficiency: 17.0},
            {hour: 14, power: 4.7, avgPower: 4.4, generation: 4.7, avgGeneration: 4.4, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 15, power: 3.5, avgPower: 3.2, generation: 3.5, avgGeneration: 3.2, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 16, power: 2.2, avgPower: 2.0, generation: 2.2, avgGeneration: 2.0, efficiency: 18.1, avgEfficiency: 17.6},
            {hour: 17, power: 1.0, avgPower: 0.9, generation: 1.0, avgGeneration: 0.9, efficiency: 18.3, avgEfficiency: 17.8},
            {hour: 18, power: 0.2, avgPower: 0.2, generation: 0.2, avgGeneration: 0.2, efficiency: 18.2, avgEfficiency: 17.7},
            {hour: 19, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 20, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 21, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 22, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 23, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0}
        ];

        baseDataSets.day4 = [
            // 2025-08-04 数据 (阴天)
            {hour: 0, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 1, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 2, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 3, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 4, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 5, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 6, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 7, power: 0.2, avgPower: 0.2, generation: 0.2, avgGeneration: 0.2, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 8, power: 1.1, avgPower: 1.0, generation: 1.1, avgGeneration: 1.0, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 9, power: 1.9, avgPower: 1.8, generation: 1.9, avgGeneration: 1.8, efficiency: 18.1, avgEfficiency: 17.6},
            {hour: 10, power: 2.4, avgPower: 2.2, generation: 2.4, avgGeneration: 2.2, efficiency: 18.0, avgEfficiency: 17.5},
            {hour: 11, power: 2.9, avgPower: 2.7, generation: 2.9, avgGeneration: 2.7, efficiency: 17.8, avgEfficiency: 17.3},
            {hour: 12, power: 3.1, avgPower: 2.9, generation: 3.1, avgGeneration: 2.9, efficiency: 17.6, avgEfficiency: 17.1},
            {hour: 13, power: 3.1, avgPower: 2.9, generation: 3.1, avgGeneration: 2.9, efficiency: 17.5, avgEfficiency: 17.0},
            {hour: 14, power: 2.7, avgPower: 2.5, generation: 2.7, avgGeneration: 2.5, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 15, power: 2.0, avgPower: 1.9, generation: 2.0, avgGeneration: 1.9, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 16, power: 1.3, avgPower: 1.2, generation: 1.3, avgGeneration: 1.2, efficiency: 18.1, avgEfficiency: 17.6},
            {hour: 17, power: 0.6, avgPower: 0.5, generation: 0.6, avgGeneration: 0.5, efficiency: 18.2, avgEfficiency: 17.7},
            {hour: 18, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 19, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 20, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 21, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 22, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 23, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0}
        ];

        // 添加更多天的数据
        baseDataSets.day5 = [
            // 2025-08-05 数据 (晴天)
            {hour: 0, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 1, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 2, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 3, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 4, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 5, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 6, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 7, power: 0.7, avgPower: 0.6, generation: 0.7, avgGeneration: 0.6, efficiency: 17.6, avgEfficiency: 17.1},
            {hour: 8, power: 2.2, avgPower: 2.0, generation: 2.2, avgGeneration: 2.0, efficiency: 17.8, avgEfficiency: 17.3},
            {hour: 9, power: 3.5, avgPower: 3.2, generation: 3.5, avgGeneration: 3.2, efficiency: 18.0, avgEfficiency: 17.5},
            {hour: 10, power: 4.6, avgPower: 4.3, generation: 4.6, avgGeneration: 4.3, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 11, power: 5.3, avgPower: 5.0, generation: 5.3, avgGeneration: 5.0, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 12, power: 5.6, avgPower: 5.3, generation: 5.6, avgGeneration: 5.3, efficiency: 17.5, avgEfficiency: 17.0},
            {hour: 13, power: 5.6, avgPower: 5.3, generation: 5.6, avgGeneration: 5.3, efficiency: 17.4, avgEfficiency: 16.9},
            {hour: 14, power: 5.0, avgPower: 4.7, generation: 5.0, avgGeneration: 4.7, efficiency: 17.6, avgEfficiency: 17.1},
            {hour: 15, power: 3.8, avgPower: 3.5, generation: 3.8, avgGeneration: 3.5, efficiency: 17.8, avgEfficiency: 17.3},
            {hour: 16, power: 2.4, avgPower: 2.2, generation: 2.4, avgGeneration: 2.2, efficiency: 18.0, avgEfficiency: 17.5},
            {hour: 17, power: 1.2, avgPower: 1.1, generation: 1.2, avgGeneration: 1.1, efficiency: 18.2, avgEfficiency: 17.7},
            {hour: 18, power: 0.2, avgPower: 0.2, generation: 0.2, avgGeneration: 0.2, efficiency: 18.1, avgEfficiency: 17.6},
            {hour: 19, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 20, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 21, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 22, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 23, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0}
        ];

        baseDataSets.day6 = [
            // 2025-08-06 数据
            {hour: 0, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 1, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 2, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 3, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 4, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 5, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 6, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 7, power: 0.5, avgPower: 0.4, generation: 0.5, avgGeneration: 0.4, efficiency: 17.5, avgEfficiency: 17.0},
            {hour: 8, power: 1.8, avgPower: 1.7, generation: 1.8, avgGeneration: 1.7, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 9, power: 2.9, avgPower: 2.7, generation: 2.9, avgGeneration: 2.7, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 10, power: 3.9, avgPower: 3.6, generation: 3.9, avgGeneration: 3.6, efficiency: 17.8, avgEfficiency: 17.3},
            {hour: 11, power: 4.6, avgPower: 4.3, generation: 4.6, avgGeneration: 4.3, efficiency: 17.6, avgEfficiency: 17.1},
            {hour: 12, power: 4.9, avgPower: 4.6, generation: 4.9, avgGeneration: 4.6, efficiency: 17.4, avgEfficiency: 16.9},
            {hour: 13, power: 4.9, avgPower: 4.6, generation: 4.9, avgGeneration: 4.6, efficiency: 17.3, avgEfficiency: 16.8},
            {hour: 14, power: 4.3, avgPower: 4.0, generation: 4.3, avgGeneration: 4.0, efficiency: 17.5, avgEfficiency: 17.0},
            {hour: 15, power: 3.2, avgPower: 3.0, generation: 3.2, avgGeneration: 3.0, efficiency: 17.7, avgEfficiency: 17.2},
            {hour: 16, power: 2.0, avgPower: 1.9, generation: 2.0, avgGeneration: 1.9, efficiency: 17.9, avgEfficiency: 17.4},
            {hour: 17, power: 0.9, avgPower: 0.8, generation: 0.9, avgGeneration: 0.8, efficiency: 18.1, avgEfficiency: 17.6},
            {hour: 18, power: 0.1, avgPower: 0.1, generation: 0.1, avgGeneration: 0.1, efficiency: 18.0, avgEfficiency: 17.5},
            {hour: 19, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 20, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 21, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 22, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0},
            {hour: 23, power: 0.0, avgPower: 0.0, generation: 0.0, avgGeneration: 0.0, efficiency: 0.0, avgEfficiency: 0.0}
        ];

        // 数据集数组和对应的日期
        const dataSetKeys = ['day1', 'day2', 'day3', 'day4', 'day5', 'day6'];
        const dataSetDates = ['2025年8月1日', '2025年8月2日', '2025年8月3日', '2025年8月4日', '2025年8月5日', '2025年8月6日'];

        // 实时数据（会被轻微调整）
        let realData = JSON.parse(JSON.stringify(baseDataSets.day1));

        // 数据配置
        const tabConfig = {
            generation: {
                title: '发电量',
                unit: '单位：kWh',
                field: 'generation',
                avgField: 'avgGeneration'
            },
            power: {
                title: '发电功率',
                unit: '单位：kW',
                field: 'power',
                avgField: 'avgPower'
            },
            efficiency: {
                title: '发电效率',
                unit: '单位：%',
                field: 'efficiency',
                avgField: 'avgEfficiency'
            }
        };

        // 生成微小的实时波动值
        function getMinorFluctuation(baseValue, maxPercent = 3) {
            if (baseValue === 0) return 0;
            const fluctuation = (Math.random() - 0.5) * 2 * (maxPercent / 100);
            const newValue = baseValue * (1 + fluctuation);
            return Math.max(0, newValue);
        }

        // 更新当前日期显示
        function updateCurrentDate() {
            const currentDate = dataSetDates[currentDataIndex];
            document.getElementById('currentDate').textContent = currentDate;
        }

        // 更新实时数据
        function updateRealTimeData() {
            console.log('Updating real-time data - switching to next day...');

            // 切换数据集（模拟不同日期的数据）
            currentDataIndex = (currentDataIndex + 1) % dataSetKeys.length;
            const currentDataSet = baseDataSets[dataSetKeys[currentDataIndex]];

            // 重新复制基础数据
            realData = JSON.parse(JSON.stringify(currentDataSet));

            // 对非零数据添加微小波动（保持数据的真实性）
            realData.forEach((dataPoint, index) => {
                const basePoint = currentDataSet[index];

                // 微小波动，保持数据准确性
                if (basePoint.power > 0) {
                    dataPoint.power = parseFloat(getMinorFluctuation(basePoint.power, 2).toFixed(2));
                    dataPoint.avgPower = parseFloat(getMinorFluctuation(basePoint.avgPower, 1.5).toFixed(2));
                }

                if (basePoint.generation > 0) {
                    dataPoint.generation = parseFloat(getMinorFluctuation(basePoint.generation, 2).toFixed(2));
                    dataPoint.avgGeneration = parseFloat(getMinorFluctuation(basePoint.avgGeneration, 1.5).toFixed(2));
                }

                if (basePoint.efficiency > 0) {
                    dataPoint.efficiency = parseFloat(getMinorFluctuation(basePoint.efficiency, 1.5).toFixed(1));
                    dataPoint.avgEfficiency = parseFloat(getMinorFluctuation(basePoint.avgEfficiency, 1).toFixed(1));
                }
            });

            // 更新日期
            lastUpdateTime = new Date();
            updateCurrentDate();

            // 重新渲染图表
            renderChart(true);
        }

        // 获取数据 - 所有图表都显示24小时数据
        function getData(type) {
            const config = tabConfig[type];
            const data = {
                user: [],
                avg: [],
                xAxis: []
            };

            // 所有图表类型都显示24小时数据
            for (let i = 0; i < 24; i++) {
                const dataPoint = realData[i];
                data.xAxis.push(i.toString().padStart(2, '0'));
                data.user.push(dataPoint[config.field]);

                if (config.avgField) {
                    data.avg.push(dataPoint[config.avgField]);
                }
            }

            return data;
        }

        // 计算统计数据
        function calculateStats(userData, avgData = []) {
            const validUserData = userData.filter(v => v > 0);
            const validAvgData = avgData.filter(v => v > 0);
            
            const userMax = validUserData.length > 0 ? Math.max(...validUserData) : 0;
            const userMin = validUserData.length > 0 ? Math.min(...validUserData) : 0;
            const userAvg = validUserData.length > 0 ? validUserData.reduce((a, b) => a + b, 0) / validUserData.length : 0;

            let stats = {
                userMax: userMax.toFixed(1),
                userMin: userMin.toFixed(1),
                userAvg: userAvg.toFixed(1)
            };

            if (validAvgData.length > 0) {
                const avgMax = Math.max(...validAvgData);
                const avgMin = Math.min(...validAvgData);
                const avgAverage = validAvgData.reduce((a, b) => a + b, 0) / validAvgData.length;
                
                stats.avgMax = avgMax.toFixed(1);
                stats.avgMin = avgMin.toFixed(1);
                stats.avgAverage = avgAverage.toFixed(1);
            }

            return stats;
        }

        // 渲染统计卡片
        function renderStats(stats, isUpdate = false) {
            const statsGrid = document.getElementById('statsGrid');
            const updatedClass = isUpdate ? 'updated' : '';

            // 统计卡片
            statsGrid.innerHTML = `
                <div class="stat-card ${updatedClass}">
                    <div class="stat-value">${stats.userMax}</div>
                    <div class="stat-label">本发电户最大值</div>
                </div>
                <div class="stat-card ${updatedClass}">
                    <div class="stat-value">${stats.userMin}</div>
                    <div class="stat-label">本发电户最小值</div>
                </div>
                <div class="stat-card ${updatedClass}">
                    <div class="stat-value">${stats.userAvg}</div>
                    <div class="stat-label">本发电户平均值</div>
                </div>
                <div class="stat-card ${updatedClass}">
                    <div class="stat-value">${stats.avgMax || '--'}</div>
                    <div class="stat-label">同地区(县)<br>最大值</div>
                </div>
                <div class="stat-card ${updatedClass}">
                    <div class="stat-value">${stats.avgMin || '--'}</div>
                    <div class="stat-label">同地区(县)<br>最小值</div>
                </div>
                <div class="stat-card ${updatedClass}">
                    <div class="stat-value">${stats.avgAverage || '--'}</div>
                    <div class="stat-label">同地区(县)<br>平均值</div>
                </div>
            `;

            // 更新后移除高亮效果
            if (isUpdate) {
                setTimeout(() => {
                    document.querySelectorAll('.stat-card').forEach(card => {
                        card.classList.remove('updated');
                    });
                }, 1500);
            }
        }

        // 渲染图表
        function renderChart(isUpdate = false) {
            const data = getData(currentTab);
            const config = tabConfig[currentTab];
            const stats = calculateStats(data.user, data.avg);

            // 更新标题和单位
            document.getElementById('chartTitle').textContent = config.title;
            document.getElementById('chartUnit').textContent = config.unit;

            // 隐藏辐照度信息
            const irradianceInfo = document.getElementById('irradianceInfo');
            irradianceInfo.style.display = 'none';

            // 更新图例
            const chartLegend = document.getElementById('chartLegend');
            chartLegend.innerHTML = `
                <div class="legend-item">
                    <div class="legend-color legend-green"></div>
                    <span>本发电户</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-blue"></div>
                    <span>同地区(县)平均</span>
                </div>
            `;

            // 渲染统计卡片
            renderStats(stats, isUpdate);

            // 配置图表
            let series = [
                {
                    name: '本发电户',
                    type: 'bar',
                    data: data.user,
                    itemStyle: {
                        color: '#52c41a'
                    },
                    barWidth: '35%',
                    animationDuration: isUpdate ? 800 : 1000,
                    animationEasing: 'cubicOut'
                }
            ];

            // 添加平均数据系列
            if (data.avg && data.avg.length > 0 && data.avg.some(v => v > 0)) {
                series.push({
                    name: '同地区(县)平均',
                    type: 'bar',
                    data: data.avg,
                    itemStyle: {
                        color: '#1890ff'
                    },
                    barWidth: '35%',
                    animationDuration: isUpdate ? 800 : 1000,
                    animationEasing: 'cubicOut'
                });
            }

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        let result = params[0].axisValue + ':00<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + param.value;
                            if (currentTab === 'power') result += ' kW';
                            else if (currentTab === 'generation') result += ' kWh';
                            else if (currentTab === 'efficiency') result += ' %';
                            result += '<br/>';
                        });
                        return result;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.xAxis,
                    axisLine: {
                        lineStyle: {
                            color: '#e8e8e8'
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#f0f0f0'
                        }
                    }
                },
                series: series
            };

            chart.setOption(option);
        }

        // 切换标签页
        function switchTab(tabName) {
            currentTab = tabName;

            // 更新标签样式
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 重新渲染图表
            renderChart();
        }

        // 启动实时刷新
        function startRealTimeRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            refreshInterval = setInterval(updateRealTimeData, 5000);
            console.log('Real-time refresh started - updating every 5 seconds');
        }

        // 停止实时刷新
        function stopRealTimeRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
                console.log('Real-time refresh stopped');
            }
        }

        // 初始化
        function init() {
            console.log('Initializing solar monitoring system with precise data...');
            
            chart = echarts.init(document.getElementById('chart'));

            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    switchTab(tab.dataset.tab);
                });
            });

            updateCurrentDate();
            renderChart();
            startRealTimeRefresh();

            window.addEventListener('resize', () => {
                chart.resize();
            });

            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    stopRealTimeRefresh();
                } else {
                    startRealTimeRefresh();
                }
            });
        }

        document.addEventListener('DOMContentLoaded', init);

        window.addEventListener('beforeunload', () => {
            stopRealTimeRefresh();
        });
    </script>
</body>
</html>
